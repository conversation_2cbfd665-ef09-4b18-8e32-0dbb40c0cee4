*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246/0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246/0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
*,
:after,
:before {
    box-sizing: border-box;
    border: 0 solid #e5e7eb
}

:after,
:before {
    --tw-content: ""
}

:host,
html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    font-family: var(--font-cairo);
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: transparent
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
pre,
samp {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,
legend {
    padding: 0
}

menu,
ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

[role=button],
button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display: block;
    vertical-align: middle
}

img,
video {
    max-width: 100%;
    height: auto
}

[hidden]:where(:not([hidden=until-found])) {
    display: none
}

:root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 56%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 56%;
    --radius: 0.5rem
}

.dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 56%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%
}

* {
    border-color: hsl(var(--border))
}

body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: var(--font-cairo), sans-serif
}

.container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 2rem;
    padding-left: 2rem
}

@media (min-width:1400px) {
    .container {
        max-width: 1400px
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.visible {
    visibility: visible
}

.invisible {
    visibility: hidden
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.inset-0 {
    inset: 0
}

.inset-x-0 {
    left: 0;
    right: 0
}

.inset-y-0 {
    top: 0;
    bottom: 0
}

.-bottom-12 {
    bottom: -3rem
}

.-left-12 {
    left: -3rem
}

.-right-12 {
    right: -3rem
}

.-top-12 {
    top: -3rem
}

.bottom-0 {
    bottom: 0
}

.bottom-6 {
    bottom: 1.5rem
}

.left-0 {
    left: 0
}

.left-1 {
    left: .25rem
}

.left-1\/2 {
    left: 50%
}

.left-2 {
    left: .5rem
}

.left-6 {
    left: 1.5rem
}

.left-\[50\%\] {
    left: 50%
}

.right-0 {
    right: 0
}

.right-1 {
    right: .25rem
}

.right-2 {
    right: .5rem
}

.right-3 {
    right: .75rem
}

.right-4 {
    right: 1rem
}

.top-0 {
    top: 0
}

.top-1\.5 {
    top: .375rem
}

.top-1\/2 {
    top: 50%
}

.top-2 {
    top: .5rem
}

.top-3\.5 {
    top: .875rem
}

.top-4 {
    top: 1rem
}

.top-\[1px\] {
    top: 1px
}

.top-\[50\%\] {
    top: 50%
}

.top-\[60\%\] {
    top: 60%
}

.top-full {
    top: 100%
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-50 {
    z-index: 50
}

.z-\[100\] {
    z-index: 100
}

.z-\[1\] {
    z-index: 1
}

.-mx-1 {
    margin-left: -.25rem;
    margin-right: -.25rem
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem
}

.mx-3\.5 {
    margin-left: .875rem;
    margin-right: .875rem
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-0\.5 {
    margin-top: .125rem;
    margin-bottom: .125rem
}

.my-1 {
    margin-top: .25rem;
    margin-bottom: .25rem
}

.-ml-4 {
    margin-left: -1rem
}

.-mt-4 {
    margin-top: -1rem
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-12 {
    margin-bottom: 3rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.ml-1 {
    margin-left: .25rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-3 {
    margin-left: .75rem
}

.ml-auto {
    margin-left: auto
}

.mr-1 {
    margin-right: .25rem
}

.mr-2 {
    margin-right: .5rem
}

.mt-0\.5 {
    margin-top: .125rem
}

.mt-1\.5 {
    margin-top: .375rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-24 {
    margin-top: 6rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-auto {
    margin-top: auto
}

.block {
    display: block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.table {
    display: table
}

.grid {
    display: grid
}

.hidden {
    display: none
}

.aspect-square {
    aspect-ratio: 1/1
}

.aspect-video {
    aspect-ratio: 16/9
}

.size-4 {
    width: 1rem;
    height: 1rem
}

.h-1\.5 {
    height: .375rem
}

.h-10 {
    height: 2.5rem
}

.h-11 {
    height: 2.75rem
}

.h-12 {
    height: 3rem
}

.h-16 {
    height: 4rem
}

.h-2 {
    height: .5rem
}

.h-2\.5 {
    height: .625rem
}

.h-3 {
    height: .75rem
}

.h-3\.5 {
    height: .875rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-7 {
    height: 1.75rem
}

.h-8 {
    height: 2rem
}

.h-9 {
    height: 2.25rem
}

.h-\[1px\] {
    height: 1px
}

.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height)
}

.h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height)
}

.h-auto {
    height: auto
}

.h-full {
    height: 100%
}

.h-px {
    height: 1px
}

.h-svh {
    height: 100svh
}

.max-h-96 {
    max-height: 24rem
}

.max-h-\[300px\] {
    max-height: 300px
}

.max-h-screen {
    max-height: 100vh
}

.min-h-0 {
    min-height: 0
}

.min-h-\[80px\] {
    min-height: 80px
}

.min-h-screen {
    min-height: 100vh
}

.min-h-svh {
    min-height: 100svh
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-10 {
    width: 2.5rem
}

.w-11 {
    width: 2.75rem
}

.w-12 {
    width: 3rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-2\.5 {
    width: .625rem
}

.w-3 {
    width: .75rem
}

.w-3\.5 {
    width: .875rem
}

.w-3\/4 {
    width: 75%
}

.w-4 {
    width: 1rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-64 {
    width: 16rem
}

.w-7 {
    width: 1.75rem
}

.w-72 {
    width: 18rem
}

.w-8 {
    width: 2rem
}

.w-9 {
    width: 2.25rem
}

.w-\[--sidebar-width\] {
    width: var(--sidebar-width)
}

.w-\[100px\] {
    width: 100px
}

.w-\[1px\] {
    width: 1px
}

.w-auto {
    width: auto
}

.w-full {
    width: 100%
}

.w-max {
    width: max-content
}

.w-px {
    width: 1px
}

.min-w-0 {
    min-width: 0
}

.min-w-10 {
    min-width: 2.5rem
}

.min-w-11 {
    min-width: 2.75rem
}

.min-w-5 {
    min-width: 1.25rem
}

.min-w-9 {
    min-width: 2.25rem
}

.min-w-\[12rem\] {
    min-width: 12rem
}

.min-w-\[8rem\] {
    min-width: 8rem
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width)
}

.max-w-3xl {
    max-width: 48rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-5xl {
    max-width: 64rem
}

.max-w-6xl {
    max-width: 72rem
}

.max-w-\[--skeleton-width\] {
    max-width: var(--skeleton-width)
}

.max-w-full {
    max-width: 100%
}

.max-w-lg {
    max-width: 32rem
}

.max-w-max {
    max-width: max-content
}

.max-w-xl {
    max-width: 36rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-shrink-0,
.shrink-0 {
    flex-shrink: 0
}

.grow {
    flex-grow: 1
}

.grow-0 {
    flex-grow: 0
}

.basis-full {
    flex-basis: 100%
}

.caption-bottom {
    caption-side: bottom
}

.border-collapse {
    border-collapse: collapse
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2,
.-translate-x-px {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-x-px {
    --tw-translate-x: -1px
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.-translate-y-1\/2,
.translate-x-\[-50\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[-50\%\] {
    --tw-translate-x: -50%
}

.translate-x-px {
    --tw-translate-x: 1px
}

.translate-x-px,
.translate-y-\[-50\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-\[-50\%\] {
    --tw-translate-y: -50%
}

.rotate-45 {
    --tw-rotate: 45deg
}

.rotate-45,
.rotate-90 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-90 {
    --tw-rotate: 90deg
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.animate-spin {
    animation: spin 1s linear infinite
}

.cursor-default {
    cursor: default
}

.cursor-pointer {
    cursor: pointer
}

.touch-none {
    touch-action: none
}

.select-none {
    user-select: none
}

.list-none {
    list-style-type: none
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.flex-wrap {
    flex-wrap: wrap
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.items-stretch {
    align-items: stretch
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-1 {
    gap: .25rem
}

.gap-1\.5 {
    gap: .375rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-8 {
    gap: 2rem
}

.space-x-1>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.25rem * var(--tw-space-x-reverse));
    margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-4>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-6>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-1>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.25rem * var(--tw-space-y-reverse))
}

.space-y-1\.5>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.375rem * var(--tw-space-y-reverse))
}

.space-y-2>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.5rem * var(--tw-space-y-reverse))
}

.space-y-3>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.75rem * var(--tw-space-y-reverse))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse))
}

.space-y-6>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
}

.space-x-reverse>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 1
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-x-hidden {
    overflow-x: hidden
}

.whitespace-nowrap {
    white-space: nowrap
}

.break-words {
    overflow-wrap: break-word
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-\[2px\] {
    border-radius: 2px
}

.rounded-\[inherit\] {
    border-radius: inherit
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: var(--radius)
}

.rounded-md {
    border-radius: calc(var(--radius) - 2px)
}

.rounded-sm {
    border-radius: calc(var(--radius) - 4px)
}

.rounded-t-\[10px\] {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px
}

.rounded-bl-lg {
    border-bottom-left-radius: var(--radius)
}

.rounded-tl-sm {
    border-top-left-radius: calc(var(--radius) - 4px)
}

.border {
    border-width: 1px
}

.border-2 {
    border-width: 2px
}

.border-4 {
    border-width: 4px
}

.border-\[1\.5px\] {
    border-width: 1.5px
}

.border-y {
    border-top-width: 1px
}

.border-b,
.border-y {
    border-bottom-width: 1px
}

.border-l {
    border-left-width: 1px
}

.border-r {
    border-right-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-t-4 {
    border-top-width: 4px
}

.border-dashed {
    border-style: dashed
}

.border-\[\#0583CD\] {
    --tw-border-opacity: 1;
    border-color: rgb(5 131 205/var(--tw-border-opacity, 1))
}

.border-\[\#1550ce\] {
    --tw-border-opacity: 1;
    border-color: rgb(21 80 206/var(--tw-border-opacity, 1))
}

.border-\[\#1e90ff\] {
    --tw-border-opacity: 1;
    border-color: rgb(30 144 255/var(--tw-border-opacity, 1))
}

.border-\[\#52358F\] {
    --tw-border-opacity: 1;
    border-color: rgb(82 53 143/var(--tw-border-opacity, 1))
}

.border-\[\#f0c300\] {
    --tw-border-opacity: 1;
    border-color: rgb(240 195 0/var(--tw-border-opacity, 1))
}

.border-\[--color-border\] {
    border-color: var(--color-border)
}

.border-border\/50 {
    border-color: hsl(var(--border)/.5)
}

.border-destructive {
    border-color: hsl(var(--destructive))
}

.border-destructive\/50 {
    border-color: hsl(var(--destructive)/.5)
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.border-input {
    border-color: hsl(var(--input))
}

.border-primary {
    --tw-border-opacity: 1;
    border-color: rgb(30 144 255/var(--tw-border-opacity, 1))
}

.border-purple-500 {
    --tw-border-opacity: 1;
    border-color: rgb(168 85 247/var(--tw-border-opacity, 1))
}

.border-transparent {
    border-color: transparent
}

.border-l-transparent {
    border-left-color: transparent
}

.border-t-\[\#0583CD\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(5 131 205/var(--tw-border-opacity, 1))
}

.border-t-\[\#1550ce\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(21 80 206/var(--tw-border-opacity, 1))
}

.border-t-\[\#1e90ff\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(30 144 255/var(--tw-border-opacity, 1))
}

.border-t-\[\#28a745\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(40 167 69/var(--tw-border-opacity, 1))
}

.border-t-\[\#52358F\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(82 53 143/var(--tw-border-opacity, 1))
}

.border-t-\[\#f0c300\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(240 195 0/var(--tw-border-opacity, 1))
}

.border-t-gray-400 {
    --tw-border-opacity: 1;
    border-top-color: rgb(156 163 175/var(--tw-border-opacity, 1))
}

.border-t-transparent {
    border-top-color: transparent
}

.bg-\[\#0583CD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(5 131 205/var(--tw-bg-opacity, 1))
}

.bg-\[\#0583CD\]\/10 {
    background-color: rgb(5 131 205/.1)
}

.bg-\[\#1550ce\] {
    --tw-bg-opacity: 1;
    background-color: rgb(21 80 206/var(--tw-bg-opacity, 1))
}

.bg-\[\#1550ce\]\/10 {
    background-color: rgb(21 80 206/.1)
}

.bg-\[\#1e90ff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#1e90ff\]\/10 {
    background-color: rgb(30 144 255/.1)
}

.bg-\[\#25D366\] {
    --tw-bg-opacity: 1;
    background-color: rgb(37 211 102/var(--tw-bg-opacity, 1))
}

.bg-\[\#28a745\] {
    --tw-bg-opacity: 1;
    background-color: rgb(40 167 69/var(--tw-bg-opacity, 1))
}

.bg-\[\#28a745\]\/10 {
    background-color: rgb(40 167 69/.1)
}

.bg-\[\#52358F\] {
    --tw-bg-opacity: 1;
    background-color: rgb(82 53 143/var(--tw-bg-opacity, 1))
}

.bg-\[\#52358F\]\/10 {
    background-color: rgb(82 53 143/.1)
}

.bg-\[\#52358F\]\/20 {
    background-color: rgb(82 53 143/.2)
}

.bg-\[\#f0c300\] {
    --tw-bg-opacity: 1;
    background-color: rgb(240 195 0/var(--tw-bg-opacity, 1))
}

.bg-\[\#f0c300\]\/10 {
    background-color: rgb(240 195 0/.1)
}

.bg-\[--color-bg\] {
    background-color: var(--color-bg)
}

.bg-accent {
    background-color: hsl(var(--accent))
}

.bg-background {
    background-color: hsl(var(--background))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.bg-black\/40 {
    background-color: rgb(0 0 0/.4)
}

.bg-black\/80 {
    background-color: rgb(0 0 0/.8)
}

.bg-blue-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235/var(--tw-bg-opacity, 1))
}

.bg-border {
    background-color: hsl(var(--border))
}

.bg-card {
    background-color: hsl(var(--card))
}

.bg-destructive {
    background-color: hsl(var(--destructive))
}

.bg-foreground {
    background-color: hsl(var(--foreground))
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity, 1))
}

.bg-gray-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128/var(--tw-bg-opacity, 1))
}

.bg-muted {
    background-color: hsl(var(--muted))
}

.bg-muted\/50 {
    background-color: hsl(var(--muted)/.5)
}

.bg-popover {
    background-color: hsl(var(--popover))
}

.bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.bg-purple-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(147 51 234/var(--tw-bg-opacity, 1))
}

.bg-secondary {
    background-color: hsl(var(--secondary))
}

.bg-transparent {
    background-color: transparent
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-opacity-40 {
    --tw-bg-opacity: 0.4
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.from-\[\#0583CD\]\/20 {
    --tw-gradient-from: rgb(5 131 205/0.2) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(5 131 205/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#1550ce\]\/20 {
    --tw-gradient-from: rgb(21 80 206/0.2) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(21 80 206/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#1e90ff\]\/10 {
    --tw-gradient-from: rgb(30 144 255/0.1) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(30 144 255/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#1e90ff\]\/20 {
    --tw-gradient-from: rgb(30 144 255/0.2) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(30 144 255/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#52358F\]\/20 {
    --tw-gradient-from: rgb(82 53 143/0.2) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(82 53 143/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#833AB4\] {
    --tw-gradient-from: #833AB4 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(131 58 180/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#f0c300\]\/20 {
    --tw-gradient-from: rgb(240 195 0/0.2) var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(240 195 0/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-white {
    --tw-gradient-from: #fff var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(255 255 255/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.via-\[\#FD1D1D\] {
    --tw-gradient-to: rgb(253 29 29/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #FD1D1D var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.via-gray-100 {
    --tw-gradient-to: rgb(243 244 246/0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #f3f4f6 var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.to-\[\#0583CD\]\/10 {
    --tw-gradient-to: rgb(5 131 205/0.1) var(--tw-gradient-to-position)
}

.to-\[\#1550ce\]\/10 {
    --tw-gradient-to: rgb(21 80 206/0.1) var(--tw-gradient-to-position)
}

.to-\[\#1e90ff\]\/10 {
    --tw-gradient-to: rgb(30 144 255/0.1) var(--tw-gradient-to-position)
}

.to-\[\#1e90ff\]\/5 {
    --tw-gradient-to: rgb(30 144 255/0.05) var(--tw-gradient-to-position)
}

.to-\[\#52358F\]\/10 {
    --tw-gradient-to: rgb(82 53 143/0.1) var(--tw-gradient-to-position)
}

.to-\[\#FCAF45\] {
    --tw-gradient-to: #FCAF45 var(--tw-gradient-to-position)
}

.to-\[\#f0c300\]\/10 {
    --tw-gradient-to: rgb(240 195 0/0.1) var(--tw-gradient-to-position)
}

.to-gray-200 {
    --tw-gradient-to: #e5e7eb var(--tw-gradient-to-position)
}

.fill-current {
    fill: currentColor
}

.object-cover {
    object-fit: cover
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-10 {
    padding: 2.5rem
}

.p-2 {
    padding: .5rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-6 {
    padding: 1.5rem
}

.p-8 {
    padding: 2rem
}

.p-\[1px\] {
    padding: 1px
}

.px-1 {
    padding-left: .25rem;
    padding-right: .25rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-2\.5 {
    padding-left: .625rem;
    padding-right: .625rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.py-0\.5 {
    padding-top: .125rem;
    padding-bottom: .125rem
}

.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem
}

.py-1\.5 {
    padding-top: .375rem;
    padding-bottom: .375rem
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem
}

.py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem
}

.py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pl-2\.5 {
    padding-left: .625rem
}

.pl-4 {
    padding-left: 1rem
}

.pl-8 {
    padding-left: 2rem
}

.pr-2 {
    padding-right: .5rem
}

.pr-2\.5 {
    padding-right: .625rem
}

.pr-8 {
    padding-right: 2rem
}

.pt-0 {
    padding-top: 0
}

.pt-1 {
    padding-top: .25rem
}

.pt-3 {
    padding-top: .75rem
}

.pt-4 {
    padding-top: 1rem
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.text-right {
    text-align: right
}

.align-middle {
    vertical-align: middle
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace
}

.font-sans {
    font-family: var(--font-cairo)
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem
}

.text-\[0\.8rem\] {
    font-size: .8rem
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.font-bold {
    font-weight: 700
}

.font-extrabold {
    font-weight: 800
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.italic {
    font-style: italic
}

.tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)
}

.leading-none {
    line-height: 1
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-widest {
    letter-spacing: .1em
}

.text-\[\#000\] {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity, 1))
}

.text-\[\#0583CD\] {
    --tw-text-opacity: 1;
    color: rgb(5 131 205/var(--tw-text-opacity, 1))
}

.text-\[\#1550ce\] {
    --tw-text-opacity: 1;
    color: rgb(21 80 206/var(--tw-text-opacity, 1))
}

.text-\[\#1e90ff\] {
    --tw-text-opacity: 1;
    color: rgb(30 144 255/var(--tw-text-opacity, 1))
}

.text-\[\#28a745\] {
    --tw-text-opacity: 1;
    color: rgb(40 167 69/var(--tw-text-opacity, 1))
}

.text-\[\#333333\],
.text-\[\#333\] {
    --tw-text-opacity: 1;
    color: rgb(51 51 51/var(--tw-text-opacity, 1))
}

.text-\[\#52358F\] {
    --tw-text-opacity: 1;
    color: rgb(82 53 143/var(--tw-text-opacity, 1))
}

.text-\[\#777777\] {
    --tw-text-opacity: 1;
    color: rgb(119 119 119/var(--tw-text-opacity, 1))
}

.text-\[\#f0c300\] {
    --tw-text-opacity: 1;
    color: rgb(240 195 0/var(--tw-text-opacity, 1))
}

.text-accent-foreground {
    color: hsl(var(--accent-foreground))
}

.text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246/var(--tw-text-opacity, 1))
}

.text-card-foreground {
    color: hsl(var(--card-foreground))
}

.text-current {
    color: currentColor
}

.text-destructive {
    color: hsl(var(--destructive))
}

.text-destructive-foreground {
    color: hsl(var(--destructive-foreground))
}

.text-foreground {
    color: hsl(var(--foreground))
}

.text-foreground\/50 {
    color: hsl(var(--foreground)/.5)
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity, 1))
}

.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55/var(--tw-text-opacity, 1))
}

.text-muted-foreground {
    color: hsl(var(--muted-foreground))
}

.text-popover-foreground {
    color: hsl(var(--popover-foreground))
}

.text-primary {
    --tw-text-opacity: 1;
    color: rgb(30 144 255/var(--tw-text-opacity, 1))
}

.text-primary-foreground {
    color: hsl(var(--primary-foreground))
}

.text-secondary-foreground {
    color: hsl(var(--secondary-foreground))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.text-white\/90 {
    color: rgb(255 255 255/.9)
}

.line-through {
    text-decoration-line: line-through
}

.underline-offset-4 {
    text-underline-offset: 4px
}

.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.opacity-0 {
    opacity: 0
}

.opacity-50 {
    opacity: .5
}

.opacity-60 {
    opacity: .6
}

.opacity-70 {
    opacity: .7
}

.opacity-90 {
    opacity: .9
}

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
    --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color)
}

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\],
.shadow-lg {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0/0.1), 0 4px 6px -4px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0/0.1), 0 2px 4px -2px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-md,
.shadow-none {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0/0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-sm,
.shadow-xl {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0/0.1), 0 8px 10px -6px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color)
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.outline {
    outline-style: solid
}

.ring {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring,
.ring-0 {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-0 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-2 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-ring {
    --tw-ring-color: hsl(var(--ring))
}

.ring-offset-background {
    --tw-ring-offset-color: hsl(var(--background))
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[left\2c right\2c width\] {
    transition-property: left, right, width;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[margin\2c opa\] {
    transition-property: margin, opa;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[width\2c height\2c padding\] {
    transition-property: width, height, padding;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[width\] {
    transition-property: width;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.duration-1000 {
    transition-duration: 1s
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-linear {
    transition-timing-function: linear
}

@keyframes enter {
    0% {
        opacity: var(--tw-enter-opacity, 1);
        transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))
    }
}

@keyframes exit {
    to {
        opacity: var(--tw-exit-opacity, 1);
        transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))
    }
}

.animate-in {
    animation-name: enter;
    animation-duration: .15s;
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial
}

.fade-in-0 {
    --tw-enter-opacity: 0
}

.fade-in-80 {
    --tw-enter-opacity: 0.8
}

.zoom-in-95 {
    --tw-enter-scale: .95
}

.duration-1000 {
    animation-duration: 1s
}

.duration-200 {
    animation-duration: .2s
}

.duration-300 {
    animation-duration: .3s
}

.ease-in-out {
    animation-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-linear {
    animation-timing-function: linear
}

.file\:border-0::file-selector-button {
    border-width: 0
}

.file\:bg-transparent::file-selector-button {
    background-color: transparent
}

.file\:text-sm::file-selector-button {
    font-size: .875rem;
    line-height: 1.25rem
}

.file\:font-medium::file-selector-button {
    font-weight: 500
}

.file\:text-foreground::file-selector-button {
    color: hsl(var(--foreground))
}

.placeholder\:text-muted-foreground::placeholder {
    color: hsl(var(--muted-foreground))
}

.after\:absolute:after {
    content: var(--tw-content);
    position: absolute
}

.after\:-inset-2:after {
    content: var(--tw-content);
    inset: -.5rem
}

.after\:inset-y-0:after {
    content: var(--tw-content);
    top: 0;
    bottom: 0
}

.after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%
}

.after\:w-1:after {
    content: var(--tw-content);
    width: .25rem
}

.after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px
}

.after\:-translate-x-1\/2:after {
    content: var(--tw-content);
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.first\:rounded-l-md:first-child {
    border-top-left-radius: calc(var(--radius) - 2px);
    border-bottom-left-radius: calc(var(--radius) - 2px)
}

.first\:border-l:first-child {
    border-left-width: 1px
}

.last\:rounded-r-md:last-child {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px)
}

.focus-within\:relative:focus-within {
    position: relative
}

.focus-within\:z-20:focus-within {
    z-index: 20
}

.hover\:bg-\[\#0583CD\]\/10:hover {
    background-color: rgb(5 131 205/.1)
}

.hover\:bg-\[\#0583CD\]\/90:hover {
    background-color: rgb(5 131 205/.9)
}

.hover\:bg-\[\#1550ce\]\/10:hover {
    background-color: rgb(21 80 206/.1)
}

.hover\:bg-\[\#1550ce\]\/90:hover {
    background-color: rgb(21 80 206/.9)
}

.hover\:bg-\[\#1e90ff\]\/10:hover {
    background-color: rgb(30 144 255/.1)
}

.hover\:bg-\[\#1e90ff\]\/90:hover {
    background-color: rgb(30 144 255/.9)
}

.hover\:bg-\[\#20BD5C\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(32 189 92/var(--tw-bg-opacity, 1))
}

.hover\:bg-\[\#28a745\]\/90:hover {
    background-color: rgb(40 167 69/.9)
}

.hover\:bg-\[\#52358F\]\/10:hover {
    background-color: rgb(82 53 143/.1)
}

.hover\:bg-\[\#52358F\]\/90:hover {
    background-color: rgb(82 53 143/.9)
}

.hover\:bg-\[\#f0c300\]\/10:hover {
    background-color: rgb(240 195 0/.1)
}

.hover\:bg-\[\#f0c300\]\/90:hover {
    background-color: rgb(240 195 0/.9)
}

.hover\:bg-accent:hover {
    background-color: hsl(var(--accent))
}

.hover\:bg-blue-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(29 78 216/var(--tw-bg-opacity, 1))
}

.hover\:bg-destructive\/80:hover {
    background-color: hsl(var(--destructive)/.8)
}

.hover\:bg-destructive\/90:hover {
    background-color: hsl(var(--destructive)/.9)
}

.hover\:bg-gray-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99/var(--tw-bg-opacity, 1))
}

.hover\:bg-muted:hover {
    background-color: hsl(var(--muted))
}

.hover\:bg-muted\/50:hover {
    background-color: hsl(var(--muted)/.5)
}

.hover\:bg-primary:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.hover\:bg-primary\/80:hover {
    background-color: rgb(30 144 255/.8)
}

.hover\:bg-primary\/90:hover {
    background-color: rgb(30 144 255/.9)
}

.hover\:bg-purple-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(126 34 206/var(--tw-bg-opacity, 1))
}

.hover\:bg-secondary:hover {
    background-color: hsl(var(--secondary))
}

.hover\:bg-secondary\/80:hover {
    background-color: hsl(var(--secondary)/.8)
}

.hover\:bg-white\/90:hover {
    background-color: rgb(255 255 255/.9)
}

.hover\:text-\[\#0583CD\]:hover {
    --tw-text-opacity: 1;
    color: rgb(5 131 205/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#1550ce\]:hover {
    --tw-text-opacity: 1;
    color: rgb(21 80 206/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#1e90ff\]:hover {
    --tw-text-opacity: 1;
    color: rgb(30 144 255/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#52358F\]:hover {
    --tw-text-opacity: 1;
    color: rgb(82 53 143/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#f0c300\]:hover {
    --tw-text-opacity: 1;
    color: rgb(240 195 0/var(--tw-text-opacity, 1))
}

.hover\:text-accent-foreground:hover {
    color: hsl(var(--accent-foreground))
}

.hover\:text-foreground:hover {
    color: hsl(var(--foreground))
}

.hover\:text-muted-foreground:hover {
    color: hsl(var(--muted-foreground))
}

.hover\:text-primary-foreground:hover {
    color: hsl(var(--primary-foreground))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-100:hover {
    opacity: 1
}

.hover\:opacity-90:hover {
    opacity: .9
}

.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
    --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
    --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color)
}

.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover,
.hover\:shadow-lg:hover {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.hover\:shadow-lg:hover {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0/0.1), 0 4px 6px -4px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0/0.1), 0 2px 4px -2px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.focus\:bg-accent:focus {
    background-color: hsl(var(--accent))
}

.focus\:bg-primary:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.focus\:text-accent-foreground:focus {
    color: hsl(var(--accent-foreground))
}

.focus\:text-primary-foreground:focus {
    color: hsl(var(--primary-foreground))
}

.focus\:opacity-100:focus {
    opacity: 1
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-\[\#0583CD\]:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(5 131 205/var(--tw-ring-opacity, 1))
}

.focus\:ring-\[\#1e90ff\]:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(30 144 255/var(--tw-ring-opacity, 1))
}

.focus\:ring-\[\#52358F\]:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(82 53 143/var(--tw-ring-opacity, 1))
}

.focus\:ring-ring:focus {
    --tw-ring-color: hsl(var(--ring))
}

.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px
}

.focus-visible\:outline-none:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus-visible\:ring-1:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: hsl(var(--ring))
}

.focus-visible\:ring-offset-1:focus-visible {
    --tw-ring-offset-width: 1px
}

.focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px
}

.focus-visible\:ring-offset-background:focus-visible {
    --tw-ring-offset-color: hsl(var(--background))
}

.disabled\:pointer-events-none:disabled {
    pointer-events: none
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
}

.disabled\:opacity-50:disabled {
    opacity: .5
}

.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
    opacity: 1
}

.group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:opacity-100,
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
    opacity: 1
}

.group:hover .group-hover\:brightness-75 {
    --tw-brightness: brightness(.75);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
    border-color: hsl(var(--muted)/.4)
}

.group.toaster .group-\[\.toaster\]\:border-border {
    border-color: hsl(var(--border))
}

.group.toast .group-\[\.toast\]\:bg-muted {
    background-color: hsl(var(--muted))
}

.group.toast .group-\[\.toast\]\:bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.group.toaster .group-\[\.toaster\]\:bg-background {
    background-color: hsl(var(--background))
}

.group.destructive .group-\[\.destructive\]\:text-red-300 {
    --tw-text-opacity: 1;
    color: rgb(252 165 165/var(--tw-text-opacity, 1))
}

.group.toast .group-\[\.toast\]\:text-muted-foreground {
    color: hsl(var(--muted-foreground))
}

.group.toast .group-\[\.toast\]\:text-primary-foreground {
    color: hsl(var(--primary-foreground))
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
    color: hsl(var(--foreground))
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0/0.1), 0 4px 6px -4px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
    border-color: hsl(var(--destructive)/.3)
}

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
    background-color: hsl(var(--destructive))
}

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
    color: hsl(var(--destructive-foreground))
}

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
    --tw-text-opacity: 1;
    color: rgb(254 242 242/var(--tw-text-opacity, 1))
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus {
    --tw-ring-color: hsl(var(--destructive))
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(248 113 113/var(--tw-ring-opacity, 1))
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus {
    --tw-ring-offset-color: #dc2626
}

.peer:disabled~.peer-disabled\:cursor-not-allowed {
    cursor: not-allowed
}

.peer:disabled~.peer-disabled\:opacity-70 {
    opacity: .7
}

.has-\[\:disabled\]\:opacity-50:has(:disabled) {
    opacity: .5
}

.group\/menu-item:has([data-sidebar=menu-action]) .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8 {
    padding-right: 2rem
}

.aria-disabled\:pointer-events-none[aria-disabled=true] {
    pointer-events: none
}

.aria-disabled\:opacity-50[aria-disabled=true] {
    opacity: .5
}

.aria-selected\:bg-accent[aria-selected=true] {
    background-color: hsl(var(--accent))
}

.aria-selected\:bg-accent\/50[aria-selected=true] {
    background-color: hsl(var(--accent)/.5)
}

.aria-selected\:text-accent-foreground[aria-selected=true] {
    color: hsl(var(--accent-foreground))
}

.aria-selected\:text-muted-foreground[aria-selected=true] {
    color: hsl(var(--muted-foreground))
}

.aria-selected\:opacity-100[aria-selected=true] {
    opacity: 1
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled=true],
.data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction=vertical] {
    height: 1px
}

.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction=vertical] {
    width: 100%
}

.data-\[side\=bottom\]\:translate-y-1[data-side=bottom] {
    --tw-translate-y: 0.25rem
}

.data-\[side\=bottom\]\:translate-y-1[data-side=bottom],
.data-\[side\=left\]\:-translate-x-1[data-side=left] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[side\=left\]\:-translate-x-1[data-side=left] {
    --tw-translate-x: -0.25rem
}

.data-\[side\=right\]\:translate-x-1[data-side=right] {
    --tw-translate-x: 0.25rem
}

.data-\[side\=right\]\:translate-x-1[data-side=right],
.data-\[side\=top\]\:-translate-y-1[data-side=top] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[side\=top\]\:-translate-y-1[data-side=top] {
    --tw-translate-y: -0.25rem
}

.data-\[state\=checked\]\:translate-x-5[data-state=checked] {
    --tw-translate-x: 1.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[state\=unchecked\]\:translate-x-0[data-state=unchecked],
.data-\[swipe\=cancel\]\:translate-x-0[data-swipe=cancel] {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe=end] {
    --tw-translate-x: var(--radix-toast-swipe-end-x)
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe=end],
.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe=move] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe=move] {
    --tw-translate-x: var(--radix-toast-swipe-move-x)
}

.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction=vertical] {
    flex-direction: column
}

.data-\[active\]\:bg-accent\/50[data-active] {
    background-color: hsl(var(--accent)/.5)
}

.data-\[selected\=\'true\'\]\:bg-accent[data-selected=true] {
    background-color: hsl(var(--accent))
}

.data-\[state\=active\]\:bg-background[data-state=active] {
    background-color: hsl(var(--background))
}

.data-\[state\=checked\]\:bg-primary[data-state=checked] {
    --tw-bg-opacity: 1;
    background-color: rgb(30 144 255/var(--tw-bg-opacity, 1))
}

.data-\[state\=on\]\:bg-accent[data-state=on],
.data-\[state\=open\]\:bg-accent[data-state=open] {
    background-color: hsl(var(--accent))
}

.data-\[state\=open\]\:bg-accent\/50[data-state=open] {
    background-color: hsl(var(--accent)/.5)
}

.data-\[state\=open\]\:bg-secondary[data-state=open] {
    background-color: hsl(var(--secondary))
}

.data-\[state\=selected\]\:bg-muted[data-state=selected] {
    background-color: hsl(var(--muted))
}

.data-\[state\=unchecked\]\:bg-input[data-state=unchecked] {
    background-color: hsl(var(--input))
}

.data-\[active\=true\]\:font-medium[data-active=true] {
    font-weight: 500
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected=true] {
    color: hsl(var(--accent-foreground))
}

.data-\[state\=active\]\:text-foreground[data-state=active] {
    color: hsl(var(--foreground))
}

.data-\[state\=checked\]\:text-primary-foreground[data-state=checked] {
    color: hsl(var(--primary-foreground))
}

.data-\[state\=on\]\:text-accent-foreground[data-state=on],
.data-\[state\=open\]\:text-accent-foreground[data-state=open] {
    color: hsl(var(--accent-foreground))
}

.data-\[state\=open\]\:text-muted-foreground[data-state=open] {
    color: hsl(var(--muted-foreground))
}

.data-\[disabled\=true\]\:opacity-50[data-disabled=true],
.data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5
}

.data-\[state\=open\]\:opacity-100[data-state=open] {
    opacity: 1
}

.data-\[state\=active\]\:shadow-sm[data-state=active] {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0/0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.data-\[swipe\=move\]\:transition-none[data-swipe=move] {
    transition-property: none
}

.data-\[state\=closed\]\:duration-300[data-state=closed] {
    transition-duration: .3s
}

.data-\[state\=open\]\:duration-500[data-state=open] {
    transition-duration: .5s
}

.data-\[motion\^\=from-\]\:animate-in[data-motion^=from-],
.data-\[state\=open\]\:animate-in[data-state=open],
.data-\[state\=visible\]\:animate-in[data-state=visible] {
    animation-name: enter;
    animation-duration: .15s;
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial
}

.data-\[motion\^\=to-\]\:animate-out[data-motion^=to-],
.data-\[state\=closed\]\:animate-out[data-state=closed],
.data-\[state\=hidden\]\:animate-out[data-state=hidden],
.data-\[swipe\=end\]\:animate-out[data-swipe=end] {
    animation-name: exit;
    animation-duration: .15s;
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial
}

.data-\[motion\^\=from-\]\:fade-in[data-motion^=from-] {
    --tw-enter-opacity: 0
}

.data-\[motion\^\=to-\]\:fade-out[data-motion^=to-],
.data-\[state\=closed\]\:fade-out-0[data-state=closed] {
    --tw-exit-opacity: 0
}

.data-\[state\=closed\]\:fade-out-80[data-state=closed] {
    --tw-exit-opacity: 0.8
}

.data-\[state\=hidden\]\:fade-out[data-state=hidden] {
    --tw-exit-opacity: 0
}

.data-\[state\=open\]\:fade-in-0[data-state=open],
.data-\[state\=visible\]\:fade-in[data-state=visible] {
    --tw-enter-opacity: 0
}

.data-\[state\=closed\]\:zoom-out-95[data-state=closed] {
    --tw-exit-scale: .95
}

.data-\[state\=open\]\:zoom-in-90[data-state=open] {
    --tw-enter-scale: .9
}

.data-\[state\=open\]\:zoom-in-95[data-state=open] {
    --tw-enter-scale: .95
}

.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion=from-end] {
    --tw-enter-translate-x: 13rem
}

.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion=from-start] {
    --tw-enter-translate-x: -13rem
}

.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion=to-end] {
    --tw-exit-translate-x: 13rem
}

.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion=to-start] {
    --tw-exit-translate-x: -13rem
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom] {
    --tw-enter-translate-y: -0.5rem
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side=left] {
    --tw-enter-translate-x: 0.5rem
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side=right] {
    --tw-enter-translate-x: -0.5rem
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top] {
    --tw-enter-translate-y: 0.5rem
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state=closed] {
    --tw-exit-translate-y: 100%
}

.data-\[state\=closed\]\:slide-out-to-left[data-state=closed] {
    --tw-exit-translate-x: -100%
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state=closed] {
    --tw-exit-translate-x: -50%
}

.data-\[state\=closed\]\:slide-out-to-right-full[data-state=closed],
.data-\[state\=closed\]\:slide-out-to-right[data-state=closed] {
    --tw-exit-translate-x: 100%
}

.data-\[state\=closed\]\:slide-out-to-top[data-state=closed] {
    --tw-exit-translate-y: -100%
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state=closed] {
    --tw-exit-translate-y: -48%
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state=open] {
    --tw-enter-translate-y: 100%
}

.data-\[state\=open\]\:slide-in-from-left[data-state=open] {
    --tw-enter-translate-x: -100%
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state=open] {
    --tw-enter-translate-x: -50%
}

.data-\[state\=open\]\:slide-in-from-right[data-state=open] {
    --tw-enter-translate-x: 100%
}

.data-\[state\=open\]\:slide-in-from-top[data-state=open] {
    --tw-enter-translate-y: -100%
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state=open] {
    --tw-enter-translate-y: -48%
}

.data-\[state\=open\]\:slide-in-from-top-full[data-state=open] {
    --tw-enter-translate-y: -100%
}

.data-\[state\=closed\]\:duration-300[data-state=closed] {
    animation-duration: .3s
}

.data-\[state\=open\]\:duration-500[data-state=open] {
    animation-duration: .5s
}

.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction=vertical]:after {
    content: var(--tw-content);
    left: 0
}

.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction=vertical]:after {
    content: var(--tw-content);
    height: .25rem
}

.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction=vertical]:after {
    content: var(--tw-content);
    width: 100%
}

.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction=vertical]:after {
    content: var(--tw-content);
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction=vertical]:after {
    content: var(--tw-content);
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group[data-collapsible=offcanvas] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
    left: calc(var(--sidebar-width) * -1)
}

.group[data-collapsible=offcanvas] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
    right: calc(var(--sidebar-width) * -1)
}

.group[data-side=left] .group-data-\[side\=left\]\:-right-4 {
    right: -1rem
}

.group[data-side=right] .group-data-\[side\=right\]\:left-0 {
    left: 0
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:-mt-8 {
    margin-top: -2rem
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:hidden {
    display: none
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:\!size-8 {
    width: 2rem !important;
    height: 2rem !important
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\] {
    width: var(--sidebar-width-icon)
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] {
    width: calc(var(--sidebar-width-icon) + 1rem)
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] {
    width: calc(var(--sidebar-width-icon) + 1rem + 2px)
}

.group[data-collapsible=offcanvas] .group-data-\[collapsible\=offcanvas\]\:w-0 {
    width: 0
}

.group[data-collapsible=offcanvas] .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group[data-side=right] .group-data-\[side\=right\]\:rotate-180,
.group[data-state=open] .group-data-\[state\=open\]\:rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:overflow-hidden {
    overflow: hidden
}

.group[data-variant=floating] .group-data-\[variant\=floating\]\:rounded-lg {
    border-radius: var(--radius)
}

.group[data-variant=floating] .group-data-\[variant\=floating\]\:border {
    border-width: 1px
}

.group[data-side=left] .group-data-\[side\=left\]\:border-r {
    border-right-width: 1px
}

.group[data-side=right] .group-data-\[side\=right\]\:border-l {
    border-left-width: 1px
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:\!p-0 {
    padding: 0 !important
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:\!p-2 {
    padding: .5rem !important
}

.group[data-collapsible=icon] .group-data-\[collapsible\=icon\]\:opacity-0 {
    opacity: 0
}

.group[data-variant=floating] .group-data-\[variant\=floating\]\:shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0/0.1), 0 1px 2px -1px rgb(0 0 0/0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.group[data-collapsible=offcanvas] .group-data-\[collapsible\=offcanvas\]\:after\:left-full:after {
    content: var(--tw-content);
    left: 100%
}

.peer\/menu-button[data-size=default]~.peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
    top: .375rem
}

.peer\/menu-button[data-size=lg]~.peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
    top: .625rem
}

.peer\/menu-button[data-size=sm]~.peer-data-\[size\=sm\]\/menu-button\:top-1 {
    top: .25rem
}

.peer[data-variant=inset]~.peer-data-\[variant\=inset\]\:min-h-\[calc\(100svh-theme\(spacing\.4\)\)\] {
    min-height: calc(100svh - 1rem)
}

.dark\:border-destructive:is(.dark *) {
    border-color: hsl(var(--destructive))
}

@media (min-width:640px) {
    .sm\:bottom-0 {
        bottom: 0
    }

    .sm\:right-0 {
        right: 0
    }

    .sm\:top-auto {
        top: auto
    }

    .sm\:mt-0 {
        margin-top: 0
    }

    .sm\:flex {
        display: flex
    }

    .sm\:max-w-sm {
        max-width: 24rem
    }

    .sm\:max-w-xl {
        max-width: 36rem
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:flex-col {
        flex-direction: column
    }

    .sm\:justify-end {
        justify-content: flex-end
    }

    .sm\:gap-2\.5 {
        gap: .625rem
    }

    .sm\:space-x-2>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-right: calc(.5rem * var(--tw-space-x-reverse));
        margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)))
    }

    .sm\:space-x-4>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-right: calc(1rem * var(--tw-space-x-reverse));
        margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
    }

    .sm\:space-y-0>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(0px * var(--tw-space-y-reverse))
    }

    .sm\:rounded-lg {
        border-radius: var(--radius)
    }

    .sm\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .sm\:py-3 {
        padding-top: .75rem;
        padding-bottom: .75rem
    }

    .sm\:text-left {
        text-align: left
    }

    .sm\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state=open] {
        --tw-enter-translate-y: 100%
    }
}

@media (min-width:768px) {
    .md\:absolute {
        position: absolute
    }

    .md\:mb-8 {
        margin-bottom: 2rem
    }

    .md\:block {
        display: block
    }

    .md\:flex {
        display: flex
    }

    .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
        width: var(--radix-navigation-menu-viewport-width)
    }

    .md\:w-auto {
        width: auto
    }

    .md\:max-w-2xl {
        max-width: 42rem
    }

    .md\:max-w-5xl {
        max-width: 64rem
    }

    .md\:max-w-\[420px\] {
        max-width: 420px
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .md\:space-y-6>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
    }

    .md\:p-8 {
        padding: 2rem
    }

    .md\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .md\:py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem
    }

    .md\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem
    }

    .md\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .md\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .md\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .md\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .md\:opacity-0 {
        opacity: 0
    }

    .after\:md\:hidden:after {
        content: var(--tw-content);
        display: none
    }

    .peer[data-variant=inset]~.md\:peer-data-\[variant\=inset\]\:m-2 {
        margin: .5rem
    }

    .peer[data-state=collapsed][data-variant=inset]~.md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2 {
        margin-left: .5rem
    }

    .peer[data-variant=inset]~.md\:peer-data-\[variant\=inset\]\:ml-0 {
        margin-left: 0
    }

    .peer[data-variant=inset]~.md\:peer-data-\[variant\=inset\]\:rounded-xl {
        border-radius: .75rem
    }

    .peer[data-variant=inset]~.md\:peer-data-\[variant\=inset\]\:shadow {
        --tw-shadow: 0 1px 3px 0 rgb(0 0 0/0.1), 0 1px 2px -1px rgb(0 0 0/0.1);
        --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
    }
}

@media (min-width:1024px) {
    .lg\:max-w-3xl {
        max-width: 48rem
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .lg\:text-6xl {
        font-size: 3.75rem;
        line-height: 1
    }
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: hsl(var(--accent))
}

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child {
    border-top-left-radius: calc(var(--radius) - 2px);
    border-bottom-left-radius: calc(var(--radius) - 2px)
}

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px)
}

.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside) {
    background-color: hsl(var(--accent)/.5)
}

.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px)
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
    padding-right: 0
}

.\[\&\>button\]\:hidden>button {
    display: none
}

.\[\&\>span\:last-child\]\:truncate>span:last-child {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.\[\&\>span\]\:line-clamp-1>span {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div {
    --tw-translate-y: -3px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.\[\&\>svg\]\:absolute>svg {
    position: absolute
}

.\[\&\>svg\]\:left-4>svg {
    left: 1rem
}

.\[\&\>svg\]\:top-4>svg {
    top: 1rem
}

.\[\&\>svg\]\:size-4>svg {
    width: 1rem;
    height: 1rem
}

.\[\&\>svg\]\:h-2\.5>svg {
    height: .625rem
}

.\[\&\>svg\]\:h-3>svg {
    height: .75rem
}

.\[\&\>svg\]\:h-3\.5>svg {
    height: .875rem
}

.\[\&\>svg\]\:w-2\.5>svg {
    width: .625rem
}

.\[\&\>svg\]\:w-3>svg {
    width: .75rem
}

.\[\&\>svg\]\:w-3\.5>svg {
    width: .875rem
}

.\[\&\>svg\]\:shrink-0>svg {
    flex-shrink: 0
}

.\[\&\>svg\]\:text-destructive>svg {
    color: hsl(var(--destructive))
}

.\[\&\>svg\]\:text-foreground>svg {
    color: hsl(var(--foreground))
}

.\[\&\>svg\]\:text-muted-foreground>svg {
    color: hsl(var(--muted-foreground))
}

.\[\&\>svg\~\*\]\:pl-7>svg~* {
    padding-left: 1.75rem
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
    border-bottom-width: 0
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div {
    --tw-rotate: 90deg
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div,
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
    --tw-rotate: 180deg
}

.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: hsl(var(--muted-foreground))
}

.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: hsl(var(--border)/.5)
}

.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: transparent
}

.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector,
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: hsl(var(--muted))
}

.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: transparent
}

.\[\&_\.recharts-sector\]\:outline-none .recharts-sector,
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-left: .5rem;
    padding-right: .5rem
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-top: .375rem;
    padding-bottom: .375rem
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: .75rem;
    line-height: 1rem
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    font-weight: 500
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: hsl(var(--muted-foreground))
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group] {
    padding-top: 0
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-left: .5rem;
    padding-right: .5rem
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: 1.25rem
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: 1.25rem
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: 3rem
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-left: .5rem;
    padding-right: .5rem
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: 1.25rem
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: 1.25rem
}

.\[\&_p\]\:leading-relaxed p {
    line-height: 1.625
}

.\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none
}

.\[\&_svg\]\:size-4 svg {
    width: 1rem;
    height: 1rem
}

.\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-width: 0
}

.\[\&_tr\]\:border-b tr {
    border-bottom-width: 1px
}

[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: -.5rem
}

[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize
}

[data-side=left] .\[\[data-side\=left\]_\&\]\:cursor-w-resize {
    cursor: w-resize
}

[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: -.5rem
}

[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize
}

[data-side=right] .\[\[data-side\=right\]_\&\]\:cursor-e-resize {
    cursor: e-resize
}

@font-face {
    font-family: Cairo;
    font-style: normal;
    font-weight: 200 1000;
    font-display: swap;
    src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format("woff2");
    unicode-range: u+06??, u+0750-077f, u+0870-088e, u+0890-0891, u+0897-08e1, u+08e3-08ff, u+200c-200e, u+2010-2011, u+204f, u+2e41, u+fb50-fdff, u+fe70-fe74, u+fe76-fefc, u+102e0-102fb, u+10e60-10e7e, u+10ec2-10ec4, u+10efc-10eff, u+1ee00-1ee03, u+1ee05-1ee1f, u+1ee21-1ee22, u+1ee24, u+1ee27, u+1ee29-1ee32, u+1ee34-1ee37, u+1ee39, u+1ee3b, u+1ee42, u+1ee47, u+1ee49, u+1ee4b, u+1ee4d-1ee4f, u+1ee51-1ee52, u+1ee54, u+1ee57, u+1ee59, u+1ee5b, u+1ee5d, u+1ee5f, u+1ee61-1ee62, u+1ee64, u+1ee67-1ee6a, u+1ee6c-1ee72, u+1ee74-1ee77, u+1ee79-1ee7c, u+1ee7e, u+1ee80-1ee89, u+1ee8b-1ee9b, u+1eea1-1eea3, u+1eea5-1eea9, u+1eeab-1eebb, u+1eef0-1eef1
}

@font-face {
    font-family: Cairo;
    font-style: normal;
    font-weight: 200 1000;
    font-display: swap;
    src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-family: Cairo;
    font-style: normal;
    font-weight: 200 1000;
    font-display: swap;
    src: url(/_next/static/media/01f0c602c274ea55-s.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-family: Cairo Fallback;
    src: local("Arial");
    ascent-override: 137.65%;
    descent-override: 60.32%;
    line-gap-override: 0.00%;
    size-adjust: 94.66%
}

.__className_b5b62f {
    font-family: Cairo, Cairo Fallback;
    font-style: normal
}

.__variable_b5b62f {
    --font-cairo: "Cairo", "Cairo Fallback"
}